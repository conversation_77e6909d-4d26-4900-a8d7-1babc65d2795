The OpenMEEG software is a C++ package for solving the forward
problem of electroencephalography (EEG) and magnetoencephalography (MEG).

This directory contains code to integrate OpenMEEG with FieldTrip.
It alllows you to compute EEG leadfields with the symmetric boundary
element method.

See COPYING and LICENSE for further details on the license under which 
you can use and redistribute this software.

See and try the openmeeg_eeg_leadfield_example.m example script to
compute an EEG Leadfield with OpenMEEG

