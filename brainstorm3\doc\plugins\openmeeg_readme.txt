Copyright INRIA and ENPC

Contributors: 
- <PERSON><PERSON>
- <PERSON>, Maureen.C<PERSON>.AT.inria.fr
- <PERSON>, Alexandre.Gramfort.AT.inria.fr
- <PERSON><PERSON>IVE<PERSON>, keriven.AT.certis.enpc.fr
- <PERSON>, kybic.AT.fel.cvut.cz
- <PERSON><PERSON>,
- <PERSON>, Emmanuel.Olivi.AT.inria.fr
- <PERSON>, papadop.AT.inria.fr

Contact : <EMAIL>
Web : http://openmeeg.github.io

The OpenMEEG software is a C++ package for solving the forward
problems of electroencephalography (EEG) and magnetoencephalography (MEG).

OpenMEEG is distributed under the French opensource license CeCILL-B. It is
intended to give users the freedom to modify and redistribute the software.
It is therefore compatible with popular opensource licenses such as the GPL
and BSD licenses. The CeCILL-B license imposes to anybody distributing a
software incorporating OpenMEEG the obligation to give credits (by citing the
appropriate publications), in order for all contributions to be properly
identified and acknowledged.

The references to be acknowledged are :

<PERSON><PERSON> et al. OpenMEEG: opensource software for quasistatic
bioelectromagnetics. Biomedical engineering online (2010) vol. 9 (1) pp. 45

<PERSON><PERSON><PERSON> et al. Generalized head models for MEG/EEG: boundary element method
beyond nested volumes. Phys. Med. Biol. (2006) vol. 51 pp. 1333-1346

-- CeCILL-B full license

This software is governed by the CeCILL-B license under French law and
abiding by the rules of distribution of free software.  You can  use,
modify and/ or redistribute the software under the terms of the CeCILL-B
license as circulated by CEA, CNRS and INRIA at the following URL
"http://www.cecill.info".

As a counterpart to the access to the source code and  rights to copy,
modify and redistribute granted by the license, users are provided only
with a limited warranty  and the software's authors,  the holders of the
economic rights,  and the successive licensors  have only  limited
liability.

In this respect, the user's attention is drawn to the risks associated
with loading,  using,  modifying and/or developing or reproducing the
software by the user in light of its specific status of free software,
that may mean  that it is complicated to manipulate,  and  that  also
therefore means  that it is reserved for developers  and  experienced
professionals having in-depth computer knowledge. Users are therefore
encouraged to load and test the software's suitability as regards their
requirements in conditions enabling the security of their systems and/or
data to be ensured and,  more generally, to use and operate it in the
same conditions as regards security.

The fact that you are presently reading this means that you have had
knowledge of the CeCILL-B license and that you accept its terms.